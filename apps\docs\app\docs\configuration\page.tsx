"use client"

import { motion } from "framer-motion";
import Link from "next/link";
import { CodeBlock } from "@/components/code-block";
import { Code, Zap, ExternalLink } from "lucide-react";
import { Palette } from "lucide-react";

const configCode = `// designers.config.json
{
  "theme": "light",
  "tokens": {
    "colors": true,
    "spacing": true,
    "radius": true
  },
  "integrations": ["tailwind", "figma"]
}`;

export default function DocsConfigurationPage() {
  return (
    <div className="prose-custom">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1>Configuration</h1>
        <p className="text-xl text-muted-foreground mb-4">
          Designers is highly configurable. You can enable or disable tokens, set your default theme, and configure integrations for Tailwind, Figma, and more. The <code>designers.config.json</code> file lives at the root of your project and is auto-generated by the CLI.
        </p>
        <ul className="list-disc pl-6 text-muted-foreground mb-4">
          <li>Toggle which tokens are generated (colors, spacing, radius, etc.)</li>
          <li>Set your default theme mode (light, dark, or system)</li>
          <li>Enable integrations for Tailwind, Figma, and VS Code</li>
        </ul>
        <div className="rounded-lg bg-yellow-50 dark:bg-yellow-900/20 p-4 border border-yellow-200 dark:border-yellow-700 text-yellow-900 dark:text-yellow-200 mb-6">
          <strong>Tip:</strong> After editing <code>designers.config.json</code>, re-run <code>npx designers init</code> to update your setup.
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="my-8"
      >
        <h2>Sample designers.config.json</h2>
        <CodeBlock code={configCode} language="json" />
        <h2 className="mt-8">Configuration Options</h2>
        <ul className="list-disc pl-6 text-muted-foreground mb-4">
          <li><strong>theme</strong>: <code>"light" | "dark" | "system"</code> — Default color mode</li>
          <li><strong>tokens</strong>: Enable/disable token categories (colors, spacing, radius, etc.)</li>
          <li><strong>integrations</strong>: Array of integrations to enable (e.g., <code>"tailwind"</code>, <code>"figma"</code>)</li>
        </ul>
        <div className="rounded-lg bg-green-50 dark:bg-green-900/20 p-4 border border-green-200 dark:border-green-700 text-green-900 dark:text-green-200 mt-6">
          <strong>Pro Tip:</strong> You can version control your config and share it across teams for consistent design systems.
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mt-12 p-6 glass-card"
      >
        <h3 className="text-lg font-semibold mb-3">What's Next?</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link
            href="/docs/quick-start"
            className="flex items-center space-x-2 text-primary hover:underline"
          >
            <Zap className="h-4 w-4" />
            <span>Quick Start</span>
          </Link>
          <Link
            href="/docs/design-tokens"
            className="flex items-center space-x-2 text-primary hover:underline"
          >
            <Code className="h-4 w-4" />
            <span>Design Tokens</span>
          </Link>
          <Link
            href="/docs/theming"
            className="flex items-center space-x-2 text-primary hover:underline"
          >
            <Palette className="h-4 w-4" />
            <span>Theming</span>
          </Link>
        </div>
      </motion.div>
    </div>
  );
}
